"use client";

import { ArrowRightIcon } from "lucide-react";
import { motion } from "motion/react";
import Image from "next/image";

const features = [
  {
    title: "Manage projects end-to-end",
    description:
      "Consolidate specs, milestones, tasks, and other documentation in one centralized location.",
    media: {
      type: "image" as const,
      src: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=600&fit=crop&crop=center",
      alt: "Project management dashboard interface",
    },
  },
  {
    title: "Project updates",
    description:
      "Communicate progress and project health with built-in project updates.",
    media: {
      type: "image" as const,
      src: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop&crop=center",
      alt: "Project updates and communication interface",
    },
  },
  {
    title: "Ideate and specify what to build next",
    description:
      "Write down product ideas and work together on feature specs in realtime, multiplayer product documents.",
    media: {
      type: "image" as const,
      src: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop&crop=center",
      alt: "Collaborative document editing interface",
    },
  },
  {
    title: "Collaborative documents",
    description: "Add 'style' and structure with rich text formatting options.",
    media: {
      type: "image" as const,
      src: "https://images.unsplash.com/photo-**********-ef010cbdcc31?w=800&h=600&fit=crop&crop=center",
      alt: "Rich text document editor interface",
    },
  },
];

export default function FeaturesSection() {
  return (
    <section className="bg-muted/30 relative px-4 py-24 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        {/* Section Header - Linear Style */}
        <div className="mb-16 grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <h2 className="from-primary via-primary/80 to-primary/70 bg-gradient-to-r bg-clip-text text-5xl leading-tight font-medium text-transparent">
              Made for modern product teams
            </h2>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.1 }}
            className="space-y-4"
          >
            <p className="text-muted-foreground text-lg leading-relaxed font-normal">
              Next Core is shaped by the practices and principles that
              distinguish world-class product teams from the rest: relentless
              focus, fast execution, and a commitment to the quality of craft.
            </p>
            <div className="flex items-center">
              <span className="text-primary flex cursor-pointer items-center gap-2 text-sm font-medium hover:underline">
                Make the switch <ArrowRightIcon className="h-3 w-3" />
              </span>
            </div>
          </motion.div>
        </div>

        {/* Feature Cards Grid - Linear Style */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
          className="grid grid-cols-1 gap-6 md:grid-cols-2"
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.1 }}
              transition={{
                duration: 0.6,
                ease: "easeOut",
                delay: index * 0.1,
              }}
              className="group bg-card/50 border-border/50 hover:border-border relative overflow-hidden rounded-xl border backdrop-blur-sm transition-all duration-300"
            >
              {/* Card Content */}
              <div className="space-y-4 p-6">
                <div className="space-y-2">
                  <h3 className="text-foreground text-xl font-semibold">
                    {feature.title}
                  </h3>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    {feature.description}
                  </p>
                </div>

                {/* Media Container */}
                <div className="bg-muted/20 relative aspect-[16/10] overflow-hidden rounded-lg">
                  <div className="from-background/10 to-background/30 absolute inset-0 bg-gradient-to-br" />
                  <Image
                    src={feature.media.src}
                    alt={feature.media.alt}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                    loading="lazy"
                    quality={90}
                  />

                  {/* Overlay for better text readability */}
                  <div className="from-background/60 absolute inset-0 bg-gradient-to-t via-transparent to-transparent" />

                  {/* Interface mockup overlay */}
                  <div className="absolute right-4 bottom-4 left-4">
                    <div className="bg-background/90 border-border/50 rounded-md border p-3 backdrop-blur-sm">
                      <div className="mb-2 flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-green-500" />
                        <div className="text-foreground text-xs font-medium">
                          {index === 0 && "On track"}
                          {index === 1 && "Project Health"}
                          {index === 2 && "Collaborate on ideas"}
                          {index === 3 && "Rich formatting"}
                        </div>
                      </div>
                      <div className="text-muted-foreground text-xs">
                        {index === 0 && "We are ready to launch next Thursday"}
                        {index === 1 && "All milestones on schedule"}
                        {index === 2 && "Real-time collaborative editing"}
                        {index === 3 && "Text-to-issue commands"}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
